using Builder.ProjectServicesSecurity.API.Domain;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Request;
using Builder.ProjectServicesSecurity.API.Presentation.Contracts.Response;
using Builder.ProjectServicesSecurity.API.Services;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Builder.ProjectServicesSecurity.API.Presentation.Controllers
{
    public class ProjectUserController : ApiController
    {
        private readonly IProjectUserService _projectUserService;
        public ProjectUserController(IProjectUserService projectUserService)
        {
            _projectUserService = projectUserService;
        }

        /// <summary>
        /// Assign multiple users to multiple projects
        /// </summary>
        [HttpPost]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(typeof(ProjectUserResponse), StatusCodes.Status207MultiStatus)]
        public async Task<IActionResult> AddAsync(
            [Required][FromHeader(Name = "userName")] string userName,
            [FromHeader(Name = "Custom-Countryid")] string countryIds,
            [FromBody] ProjectUserRequest projectUserRequest)
        {
            try
            {
                var responses = new ProjectUserResponse
                {
                    ProjectResponses = new List<ResponseInfo>()
                };
                var projects = new List<Project>();

                foreach (var projectId in projectUserRequest.ProjectIds)
                {
                    var userHistory = projectUserRequest.UserIds.Select(userId => new UserHistory
                    {
                        UserId = userId
                    }).ToList();

                    var project = new Project(projectId, userHistory);
                    projects.Add(project);
                }

                var projectDetails = new ProjectDetails
                {
                    Projects = projects,
                    Username = userName,
                    CountryIds = countryIds,
                    ProjectTypeId = projectUserRequest.ProjectTypeId
                };

                var result = await _projectUserService.AddAsync(projectDetails);
                GenerateAddResponse(responses, projects, result);

                if (responses != null)
                {
                    return StatusCode(StatusCodes.Status207MultiStatus, responses);
                }
                return BadRequest(responses);
            }
            catch (HttpRequestException)
            {
                return StatusCode(StatusCodes.Status403Forbidden);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
                {
                    Title = "Internal Server Error",
                    Detail = ex.Message,
                    Status = StatusCodes.Status500InternalServerError
                });
            }
        }

        /// <summary>
        /// Get all user details assigned to a project
        /// </summary>
        [HttpGet("{projectId}/{projectTypeId}")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(UserResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<UserResponse>> GetUsers(int projectId, int projectTypeId)
        {
            var response = await _projectUserService.GetAsync(projectId, projectTypeId);
            if (response != null)
            {
                var userHistoryResponse = response.Select(user => new UserHistoryResponse
                {
                    Id = user.UserId,
                    AssignedBy = user.CreatedBy,
                    AssignedOn = user.CreatedWhen ?? null
                })
                .OrderByDescending(u => u.AssignedOn)
                .ToList();

                var result = new UserResponse { ProjectId = projectId, Users = userHistoryResponse };
                return OkOrNoContentT(result);
            }
            return NotFound();
        }

        /// <summary>
        /// Get details of specific users assigned to a project
        /// </summary>
        [HttpPost("list")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(UserResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<UserResponse>> GetUserDetails([FromBody] UsersRequest users)
        {
            var userHistory = users.UserIds.Select(userId => new UserHistory
            {
                UserId = userId
            }).ToList();

            var project = new Project(users.ProjectId, userHistory);

            var response = await _projectUserService.GetAsyncList(project);
            if (response != null)
            {
                var userHistoryResponse = response.Select(user => new UserHistoryResponse
                {
                    Id = user.UserId,
                    AssignedBy = user.CreatedBy,
                    AssignedOn = user.CreatedWhen ?? null,
                    DeletedBy = user.DeletedBy,
                    DeletedOn = user.DeletedOn

                }).ToList();

                var result = new UserResponse { ProjectId = users.ProjectId, Users = userHistoryResponse };
                return OkOrNoContentT(result);
            }
            return NotFound();
        }

        /// <summary>
        /// Remove multiple users from a single project
        /// </summary>
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status207MultiStatus)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RemoveUsers(
            [Required][FromHeader(Name = "userName")] string userName, [FromHeader(Name = "Custom-Countryid")] string countryIds,
            UsersRequest users)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ValidationProblemDetails(ModelState));
            }

            var responses = new ProjectUserResponse
            {
                ProjectResponses = new List<ResponseInfo>()
            };

            try
            {
                var userHistory = users.UserIds.Select(userId => new UserHistory
                {
                    UserId = userId
                }).ToList();

                var projects = new List<Project> { new Project(users.ProjectId, userHistory) };

                var projectDetails = new ProjectDetails
                {
                    Projects = projects,
                    Username = userName,
                    CountryIds = countryIds,
                    ProjectTypeId = users.ProjectTypeId
                };

                var result = await _projectUserService.DeleteAsync(projectDetails);
                GenerateRemoveResponse(responses, users.ProjectId, result);
                if (responses != null)
                {
                    return StatusCode(StatusCodes.Status207MultiStatus, responses);
                }
                return BadRequest(responses);
            }
            catch (HttpRequestException)
            {
                return StatusCode(StatusCodes.Status403Forbidden);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
                {
                    Title = "Internal Server Error",
                    Detail = ex.Message,
                    Status = StatusCodes.Status500InternalServerError
                });
            }
        }

        /// <summary>
        /// Bulk copy distinct BP security users from source base projects to new base projects
        /// </summary>
        [HttpPost("bulkCopySecurityUsers")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(typeof(ProjectUserResponse), StatusCodes.Status207MultiStatus)]
        public async Task<IActionResult> BulkCopyAsync(
            [Required][FromHeader(Name = "userName")] string userName,
            [FromHeader(Name = "Custom-Countryid")] string countryIds,
            [FromBody] BulkCopyProjectUserRequest bulkCopyRequest)
        {
            try
            {
                var responses = new ProjectUserResponse
                {
                    ProjectResponses = new List<ResponseInfo>()
                };

                var result = await _projectUserService.BulkCopyAsync(bulkCopyRequest, userName, countryIds);
                var newBaseProjectIds = bulkCopyRequest.ProjectMappings.Select(m => m.NewBaseProjectId).ToList();
                var projects = newBaseProjectIds.Select(projectId => new Project(projectId, new List<UserHistory>())).ToList();
                var tupleResult = result.ToTuple();
                GenerateAddResponse(responses, projects, tupleResult);

                if (responses != null)
                {
                    return StatusCode(StatusCodes.Status207MultiStatus, responses);
                }
                return BadRequest(responses);
            }
            catch (HttpRequestException)
            {
                return StatusCode(StatusCodes.Status403Forbidden);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
                {
                    Title = "Internal Server Error",
                    Detail = ex.Message,
                    Status = StatusCodes.Status500InternalServerError
                });
            }
        }

        /// <summary>
        /// Remove multiple master users from all projects
        /// </summary>
        [HttpDelete("RemoveUsersFromProjects")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RemoveUsersFromAllProjects(DeleteUsersRequest userRequest)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ValidationProblemDetails(ModelState));
            }

            var responses = new ProjectUserResponse
            {
                ProjectResponses = new List<ResponseInfo>()
            };

            try
            {
                await _projectUserService.DeleteUsersAsync(userRequest.UserIds, userRequest.ProjectTypeId);
                return StatusCode(StatusCodes.Status204NoContent);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, ex);
            }
        }

        private void GenerateRemoveResponse(ProjectUserResponse responses, int projectId, Tuple<List<int>, List<int>> result)
        {
            var masterUsers = result.Item1;
            var nonMasterUsers = result.Item2;

            if (nonMasterUsers.Count > 0 && masterUsers.Count == 0)
            {
                responses.ProjectResponses.Add(new ResponseInfo
                {
                    ProjectId = projectId,
                    StatusCode = StatusCodes.Status200OK,
                    StatusMsg = "All Users Successfully Removed"
                });
            }
            else if (masterUsers.Count > 0 && nonMasterUsers.Count > 0)
            {
                string masterUserIds = string.Join(", ", masterUsers);
                responses.ProjectResponses.Add(new ResponseInfo
                {
                    ProjectId = projectId,
                    StatusCode = StatusCodes.Status200OK,
                    StatusMsg = $"Following User IDs belong to master users and skipped from removal: {masterUserIds}"
                });
            }
            else if (masterUsers.Count > 0 && nonMasterUsers.Count == 0)
            {
                responses.ProjectResponses.Add(new ResponseInfo
                {
                    ProjectId = projectId,
                    StatusCode = StatusCodes.Status403Forbidden,
                    StatusMsg = $"Operation Restricted - Master level user cannot be removed manually."
                });
            }
        }

        private void GenerateAddResponse(ProjectUserResponse responses, List<Project> projects, Tuple<List<Project>, List<Project>> result)
        {
            foreach (var project in projects)
            {
                var successfulProjects = result.Item1.Where(p => p.ProjectId == project.ProjectId);
                var failedProjects = result.Item2.Where(p => p.ProjectId == project.ProjectId);
              
                if (successfulProjects.Any() && !failedProjects.Any())
                {
                    responses.ProjectResponses.Add(new ResponseInfo
                    {
                        ProjectId = project.ProjectId,
                        StatusCode = 200,
                        StatusMsg = "All users assigned successfully"
                    });
                }
                else if (successfulProjects.Any() && failedProjects.Any())
                {
                    responses.ProjectResponses.Add(new ResponseInfo
                    {
                        ProjectId = project.ProjectId,
                        StatusCode = 200,
                        StatusMsg = "New users assigned successfully and duplicate/master users skipped"
                    });
                }
                else if (!successfulProjects.Any() && !failedProjects.Any())
                {
                    responses.ProjectResponses.Add(new ResponseInfo
                    {
                        ProjectId = project.ProjectId,
                        StatusCode = 403,
                        StatusMsg = "Either you don't have permission to assign users, or the selected users belong to master users and can't be added manually."
                    });
                }
                else
                {
                    responses.ProjectResponses.Add(new ResponseInfo
                    {
                        ProjectId = project.ProjectId,
                        StatusCode = 409,
                        StatusMsg = "User(s) already assigned to this project"
                    });
                }
            }
        }
    }
}
